'use client';

import { useEffect, useState } from 'react';

import Footer from '@/components/v2/Footer';
import BaseLayout from '@/components/v2/layouts/BaseLayout';
import WordDocumentViewer from '@/components/v2/WordDocumentViewer';
import { PaperData, readPapersFromExcel } from '@/lib/excelUtils';

export default function V2PublicationsPage() {
  const [papers, setPapers] = useState<PaperData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadPapers = async () => {
      try {
        // 加载 papers 数据
        const papersData = await readPapersFromExcel();
        setPapers(papersData);
      } catch (error) {
        console.error('Error loading papers:', error);
      } finally {
        setLoading(false);
      }
    };

    loadPapers();
  }, []);

  const handlePdfDownload = (paperId: number) => {
    const pdfUrl = `/v2/papers/${paperId.toString().padStart(3, '0')}.pdf`;
    window.open(pdfUrl, '_blank');
  };

  if (loading) {
    return (
      <BaseLayout headerType="header3" contentType="content3" showSidebar={false}>
        <div>Loading...</div>
      </BaseLayout>
    );
  }

  return (
    <>
      <BaseLayout headerType="header3" contentType="content3" showSidebar={false}>
        {/* Papers Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-4">Papers</h2>
          <p style={{ textAlign: 'justify' }}>
            {[...papers].reverse().map((paper) => (
              <span key={paper.id}>
                <br />
                {paper.id}. <span className="font-times italic text-base">‘{paper.title}’, </span>
                <span className="font-times font-bold text-base">{paper.authors}, </span>{' '}
                {paper.doi ? (
                  <a
                    href={paper.doi}
                    className="hover:underline"
                    style={{
                      color: '#FAAC58',
                      transition: 'color 0.2s ease',
                    }}
                    onMouseEnter={(e) => (e.currentTarget.style.color = '#FF7F00')}
                    onMouseLeave={(e) => (e.currentTarget.style.color = '#FAAC58')}
                  >
                    <span className="font-times italic text-base">{paper.journal}</span>
                    <span className="font-times font-bold text-base"> {paper.volume}, </span>{' '}
                    <span className="font-times text-base">{paper.pages}</span>
                    <span className="font-times font-bold  text-base">,</span>{' '}
                    <span className="font-times text-base">({paper.year})</span>
                  </a>
                ) : (
                  <span>
                    {paper.journal} {paper.year}, {paper.volume}, {paper.pages}
                  </span>
                )}{' '}
                <button
                  onClick={() => handlePdfDownload(paper.id)}
                  className="hover:underline cursor-pointer"
                  style={{
                    color: '#FAAC58',
                    transition: 'color 0.2s ease',
                  }}
                  onMouseEnter={(e) => ((e.target as HTMLElement).style.color = '#FF7F00')}
                  onMouseLeave={(e) => ((e.target as HTMLElement).style.color = '#FAAC58')}
                >
                  (PDF)
                </button>
                <br />
              </span>
            ))}
          </p>
        </section>

        {/* Books Section */}
        <section>
          <h2 className="text-2xl font-bold mb-4">Books</h2>
          <WordDocumentViewer documentPath="/v2/doc/book.docx" />
        </section>

        {/* Researcher ID */}
        <section>
          <h2 className="text-2xl font-bold mb-4">Researcher ID</h2>
          <div>
            View my publications & citations on{' '}
            <a
              href="https://www.webofscience.com/wos/author/record/G-6630-2014"
              className="text-[#FF7F00] hover:text-[#FAAC58] cursor-pointer"
            >
              Researcher ID
            </a>
          </div>
          <p className="text-right">
            <a href="#top">top &uarr;</a>
          </p>
        </section>
      </BaseLayout>
    </>
  );
}
